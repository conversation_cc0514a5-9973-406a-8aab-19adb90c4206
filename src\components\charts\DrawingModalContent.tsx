import React, {
  useCallback,
  useMemo,
  memo,
} from "react";
import { StyleSheet, View, TouchableOpacity } from "react-native";
import { BottomSheetFlatList } from "@gorhom/bottom-sheet";
import { Box, Text } from "../restyle";
import { palette, spacing } from "@/theme/theme";
import { hp, wp } from "@/utils/responsive";
import { ALL_DRAWINGS } from "@gocharting/technical-indicators/lib/constants/AllInteractives";
import { ScrollView } from "react-native-gesture-handler";
import { getDrawingIcon } from "../../../lib/utils/drawTypeIcon";

const ALL_NEEDED_DRAWINGS = ALL_DRAWINGS.filter((d) => d.name !== "Tweet");

interface DrawingModalContentProps {
  favourite: any;
  format: string;
  addToFavorite: (item: any) => void;
  drawingModalRef: React.RefObject<any>;
  context: any;
}

// Memoized drawing item component
const DrawingItem = memo(({ 
  item, 
  onPress, 
  onLongPress 
}: { 
  item: any; 
  onPress: () => void; 
  onLongPress: () => void; 
}) => (
  <TouchableOpacity
    onPress={onPress}
    onLongPress={onLongPress}
    style={styles.drawingItem}
  >
    <Box
      height={hp(8)}
      width={hp(8)}
      borderRadius="lg"
      borderWidth={1}
      borderColor="splitterLine"
      justifyContent="center"
      alignItems="center"
    >
      {getDrawingIcon(item.name, 24, 24)}
    </Box>
    <Text
      textAlign="center"
      variant="headline"
      numberOfLines={1}
      style={styles.drawingText}
    >
      {item.name.length > 12
        ? `${item.name.substring(0, 12)}...`
        : item.name}
    </Text>
  </TouchableOpacity>
));

DrawingItem.displayName = 'DrawingItem';

// Memoized category section
const CategorySection = memo(({ 
  category, 
  renderItem 
}: { 
  category: any; 
  renderItem: any; 
}) => (
  <Box px="m" mb="m">
    <Text my="m" variant="caption">
      {category.title}
    </Text>
    <BottomSheetFlatList
      data={category.data}
      horizontal
      renderItem={renderItem}
      keyExtractor={(item) => item.type?.toString() || item.name}
      contentContainerStyle={[styles.listContainer, { gap: 10 }]}
      showsHorizontalScrollIndicator={false}
      ItemSeparatorComponent={() => <View style={{ width: wp(5) }} />}
      removeClippedSubviews={true}
      maxToRenderPerBatch={10}
      windowSize={5}
      initialNumToRender={5}
    />
  </Box>
));

CategorySection.displayName = 'CategorySection';

function generateDrawingsMenu(favourite: any) {
  const order = {
    "Favorites": 1,
    "Lines & Measures": 2,
    Patterns: 3,
    "Sacred Geometry": 4,
    "Shapes & Text": 5,
    Emojis: 6,
    Icons: 7,
    Stickers: 8,
  };

  // Step 1: Group drawings by groupName
  const grouped = ALL_NEEDED_DRAWINGS.reduce((acc: any, drawing) => {
    const group = drawing.groupName;
    if (!acc[group]) acc[group] = [];
    acc[group].push(drawing);
    return acc;
  }, {});

  // Add favorites section directly from favourite.DRAWINGS
  if (favourite?.DRAWINGS?.length > 0) {
    grouped["Favorites"] = [...favourite.DRAWINGS].reverse();
  }

  // Step 2: Convert to array with key-value pairs
  const groupedArray = Object.keys(grouped).map((key) => ({
    title: key,
    data: grouped[key],
  }));

  // Step 3: Sort based on custom order
  groupedArray.sort((a, b) => {
    const orderA = order[a.title as keyof typeof order] || Infinity;
    const orderB = order[b.title as keyof typeof order] || Infinity;
    return orderA - orderB;
  });

  return groupedArray;
}

const DrawingModalContent: React.FC<DrawingModalContentProps> = memo(({
  favourite,
  format,
  addToFavorite,
  drawingModalRef,
  context,
}) => {
  // Memoize the drawings content generation
  const content = useMemo(() => generateDrawingsMenu(favourite), [favourite]);

  // Memoized add drawing function
  const addDrawing = useCallback((drawingObject: any) => {
    context.reduxActions.addDrawingObject(drawingObject);
    drawingModalRef.current?.close();
  }, [context, drawingModalRef]);

  // Memoized add to favorite function
  const handleAddToFavorite = useCallback((item: any) => {
    const favoriteItem = {
      type: "DRAWINGS",
      item: { ...item, id: item.name },
    };
    addToFavorite(favoriteItem);
  }, [addToFavorite]);

  // Memoized render item function
  const renderItem = useCallback(
    ({ item }: { item: any }) => (
      <DrawingItem
        item={item}
        onPress={() => addDrawing(item)}
        onLongPress={() => handleAddToFavorite(item)}
      />
    ),
    [addDrawing, handleAddToFavorite]
  );

  return (
    <ScrollView
      style={styles.scrollView}
      showsVerticalScrollIndicator={false}
      removeClippedSubviews={true}
    >
      {content.map((category, index) => (
        <CategorySection
          key={category.title}
          category={category}
          renderItem={renderItem}
        />
      ))}
    </ScrollView>
  );
});

DrawingModalContent.displayName = 'DrawingModalContent';

export default DrawingModalContent;

const styles = StyleSheet.create({
  scrollView: {
    flex: 1,
  },
  drawingItem: {
    flexDirection: "column",
    alignItems: "center",
    rowGap: 10,
  },
  drawingText: {
    maxWidth: hp(10),
  },
  listContainer: {
    backgroundColor: palette.white,
  },
  flatListHorizontal: {
    flexGrow: 0,
    paddingHorizontal: spacing.mx,
    marginVertical: spacing.mx,
  },
});
