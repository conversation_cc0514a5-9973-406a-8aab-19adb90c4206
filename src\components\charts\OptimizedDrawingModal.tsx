import React, {
  useCallback,
  useContext,
  useEffect,
  useMemo,
  useState,
  memo,
  lazy,
  Suspense,
} from "react";
import { StyleSheet, View, TextInput, TouchableOpacity, ActivityIndicator } from "react-native";
import BottomSheet, {
  BottomSheetFlatList,
  BottomSheetView,
} from "@gorhom/bottom-sheet";
import { Box, Text } from "../restyle";
import { Spacer } from "../spacer";
import { Backdrop } from "../backdrop";
import { palette, spacing } from "@/theme/theme";
import { hp, wp } from "@/utils/responsive";
import { SvgIcon } from "../svg-icon";
import { icons } from "@/assets/icons";
import { ScrollView } from "react-native-gesture-handler";
import { AppActions } from "@/contexts";
import { useModalPerformanceTracking } from "@/hooks/useOptimizedToolbar";

// Lazy load heavy components
const DrawingContent = lazy(() => import('./DrawingModalContent'));

interface OptimizedDrawingModalProps {
  drawingModalRef: React.RefObject<BottomSheet>;
  favourite: any;
  format: string;
  addToFavorite: (item: any) => void;
}

// Loading skeleton for drawing content
const DrawingContentSkeleton = memo(() => (
  <Box flex={1} p="m">
    {/* Search skeleton */}
    <Box
      backgroundColor="TableTileHeader"
      height={40}
      borderRadius="lg"
      mb="m"
      opacity={0.3}
    />

    {/* Categories skeleton */}
    {[...Array(6)].map((_, index) => (
      <Box key={index} mb="l">
        {/* Category title skeleton */}
        <Box
          backgroundColor="TableTileHeader"
          height={16}
          width="30%"
          borderRadius="sm"
          mb="m"
          opacity={0.3}
        />

        {/* Items skeleton */}
        <Box flexDirection="row" gap="m">
          {[...Array(4)].map((_, itemIndex) => (
            <Box key={itemIndex} alignItems="center">
              <Box
                backgroundColor="TableTileHeader"
                height={hp(8)}
                width={hp(8)}
                borderRadius="lg"
                mb="s"
                opacity={0.3}
              />
              <Box
                backgroundColor="TableTileHeader"
                height={12}
                width={60}
                borderRadius="sm"
                opacity={0.3}
              />
            </Box>
          ))}
        </Box>
      </Box>
    ))}
  </Box>
));

DrawingContentSkeleton.displayName = 'DrawingContentSkeleton';

const OptimizedDrawingModal: React.FC<OptimizedDrawingModalProps> = memo(({
  drawingModalRef,
  favourite,
  format,
  addToFavorite,
}) => {
  const context = useContext(AppActions);
  const snapPoints = useMemo(() => [hp(80)], []);
  const [isContentLoaded, setIsContentLoaded] = useState(false);
  const [isModalOpen, setIsModalOpen] = useState(false);

  // Performance tracking
  const { startModalOpen, endModalOpen } = useModalPerformanceTracking('drawing');

  // Track modal state changes
  const handleSheetChanges = useCallback((index: number) => {
    if (index >= 0 && !isModalOpen) {
      // Modal is opening
      setIsModalOpen(true);
      startModalOpen();

      // Start loading content after modal is visible
      setTimeout(() => {
        setIsContentLoaded(true);
        endModalOpen();
      }, 50); // Small delay to ensure smooth modal opening
    } else if (index < 0 && isModalOpen) {
      // Modal is closing
      setIsModalOpen(false);
      // Keep content loaded for faster subsequent opens
    }
  }, [isModalOpen, startModalOpen, endModalOpen]);

  // Preload content when modal is about to open
  useEffect(() => {
    if (isModalOpen && !isContentLoaded) {
      const timer = setTimeout(() => {
        setIsContentLoaded(true);
      }, 100);
      return () => clearTimeout(timer);
    }
  }, [isModalOpen, isContentLoaded]);

  // Memoized modal header
  const modalHeader = useMemo(() => (
    <Box pb="m" px="m">
      <Box py="m">
        <Text variant="header" textAlign="center">
          Drawings
        </Text>
      </Box>
      <Spacer size={10} />
    </Box>
  ), []);

  // Memoized search bar
  const searchBar = useMemo(() => (
    <BottomSheetView style={styles.contentContainer}>
      <Box
        flexDirection="row"
        gap="sm"
        alignItems="center"
        justifyContent="space-between"
        my="s"
      >
        <Box
          backgroundColor="TableTileHeader"
          alignItems="center"
          justifyContent="space-between"
          borderRadius="lg"
          pl="mx"
          pr="s"
          py="s"
          flex={1}
          flexDirection="row"
        >
          <Box flex={1} flexDirection="row" gap="s">
            <SvgIcon icon={icons.ic_search} size={20} />
            <TextInput
              placeholder="Search Drawings..."
              style={styles.search}
              onChangeText={(value) => console.log(value)}
            />
          </Box>
          <Box
            width={30}
            height={30}
            alignItems="center"
            justifyContent="center"
          >
            {!isContentLoaded && (
              <ActivityIndicator
                size="small"
                color={palette.primary_purple}
              />
            )}
          </Box>
        </Box>
      </Box>
    </BottomSheetView>
  ), [isContentLoaded]);

  return (
    <BottomSheet
      ref={drawingModalRef}
      index={-1}
      snapPoints={snapPoints}
      enablePanDownToClose
      backgroundStyle={{ backgroundColor: palette.white }}
      backdropComponent={Backdrop}
      onChange={handleSheetChanges}
    >
      {modalHeader}
      {searchBar}

      {/* Progressive content loading */}
      {isModalOpen && (
        <Suspense fallback={<DrawingContentSkeleton />}>
          {isContentLoaded ? (
            <DrawingContent
              favourite={favourite}
              format={format}
              addToFavorite={addToFavorite}
              drawingModalRef={drawingModalRef}
              context={context}
            />
          ) : (
            <DrawingContentSkeleton />
          )}
        </Suspense>
      )}
    </BottomSheet>
  );
});

OptimizedDrawingModal.displayName = 'OptimizedDrawingModal';

export default OptimizedDrawingModal;

const styles = StyleSheet.create({
  contentContainer: {
    alignItems: "center",
  },
  search: {
    fontSize: 16,
    lineHeight: 20,
    color: palette.headline1,
    textTransform: "uppercase",
    flex: 1,
  },
  listContainer: {
    backgroundColor: palette.white,
  },
  flatListHorizontal: {
    flexGrow: 0,
    paddingHorizontal: spacing.mx,
    marginVertical: spacing.mx,
  },
});
